<!DOCTYPE html>
<html lang="ms">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>3D Open World Game - Simple Version</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            background: #000;
            font-family: Arial, sans-serif;
            overflow: hidden;
            color: white;
        }
        
        #loading {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            z-index: 1000;
            color: white;
            font-size: 24px;
            transition: opacity 0.5s ease;
        }
        
        #loading h1 {
            margin-bottom: 20px;
            font-size: 36px;
        }
        
        #loading .spinner {
            width: 50px;
            height: 50px;
            border: 5px solid rgba(255,255,255,0.3);
            border-top: 5px solid white;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-top: 20px;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        #ui {
            position: fixed;
            top: 20px;
            left: 20px;
            color: white;
            font-size: 14px;
            z-index: 100;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.8);
            background: rgba(0,0,0,0.7);
            padding: 15px;
            border-radius: 10px;
            border: 2px solid #4CAF50;
        }
        
        #ui h3 {
            margin: 0 0 10px 0;
            color: #4CAF50;
            font-size: 18px;
        }
        
        #ui div {
            margin: 5px 0;
        }
        
        #ui .control {
            color: #FFD700;
        }
        
        #ui .info {
            color: #87CEEB;
            border-top: 1px solid #666;
            padding-top: 10px;
            margin-top: 10px;
        }
        
        #crosshair {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 20px;
            height: 20px;
            z-index: 50;
        }
        
        #crosshair::before,
        #crosshair::after {
            content: '';
            position: absolute;
            background: rgba(255,255,255,0.8);
        }
        
        #crosshair::before {
            width: 2px;
            height: 20px;
            left: 9px;
            top: 0;
        }
        
        #crosshair::after {
            width: 20px;
            height: 2px;
            left: 0;
            top: 9px;
        }
        
        canvas {
            display: block;
        }
        
        #instructions {
            position: fixed;
            bottom: 20px;
            right: 20px;
            background: rgba(0,0,0,0.7);
            color: white;
            padding: 15px;
            border-radius: 10px;
            border: 2px solid #FF6B6B;
            font-size: 14px;
            max-width: 300px;
        }
        
        #instructions h4 {
            margin: 0 0 10px 0;
            color: #FF6B6B;
        }
    </style>
</head>
<body>
    <div id="loading">
        <h1>🎮 3D Open World Game</h1>
        <div>Memuatkan Dunia 3D...</div>
        <div class="spinner"></div>
        <div style="margin-top: 20px; font-size: 16px;">Sila tunggu sebentar...</div>
    </div>
    
    <div id="ui">
        <h3>🎮 Kawalan Game</h3>
        <div class="control"><strong>WASD:</strong> Bergerak</div>
        <div class="control"><strong>Mouse:</strong> Lihat sekeliling</div>
        <div class="control"><strong>Shift:</strong> Berlari</div>
        <div class="control"><strong>Klik:</strong> Kunci kamera</div>
        <div class="control"><strong>ESC:</strong> Lepas kamera</div>
        <div class="info">
            <div id="position">Kedudukan: (0, 0, 0)</div>
            <div id="speed">Kelajuan: Berjalan</div>
        </div>
    </div>
    
    <div id="crosshair"></div>
    
    <div id="instructions">
        <h4>🚀 Cara Bermain</h4>
        <div>1. Klik pada skrin untuk mula</div>
        <div>2. Gunakan WASD untuk bergerak</div>
        <div>3. Tahan Shift untuk berlari</div>
        <div>4. Jelajahi dunia 3D!</div>
    </div>
    
    <script type="module" src="simple-game.js"></script>
</body>
</html>
