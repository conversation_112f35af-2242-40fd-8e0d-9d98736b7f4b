// 3D Open World Game - Main Game Engine
import * as THREE from 'https://esm.sh/three@0.160.0';

class OpenWorldGame {
  constructor() {
    this.scene = null;
    this.camera = null;
    this.renderer = null;
    this.clock = new THREE.Clock();

    // Player properties
    this.player = null;
    this.playerSpeed = 5;
    this.runSpeed = 10;
    this.isRunning = false;
    this.playerDirection = new THREE.Vector3();

    // Camera properties
    this.cameraDistance = 10;
    this.cameraHeight = 5;
    this.cameraAngle = { horizontal: 0, vertical: 0.3 };

    // Input handling
    this.keys = {};
    this.mouse = { x: 0, y: 0, isLocked: false };

    this.init();
  }

  init() {
    console.log('Initializing game...');
    this.setupRenderer();
    this.setupScene();
    this.setupCamera();
    this.setupLighting();
    this.createWorld();
    this.createPlayer();
    this.setupControls();
    this.hideLoading();
    this.animate();
  }

  hideLoading() {
    console.log('Hiding loading screen...');
    const loading = document.getElementById('loading');
    if (loading) {
      loading.style.opacity = '0';
      setTimeout(() => {
        loading.style.display = 'none';
        console.log('Loading screen hidden!');
      }, 500);
    }
  }

  setupRenderer() {
    this.renderer = new THREE.WebGLRenderer({ antialias: true });
    this.renderer.setSize(window.innerWidth, window.innerHeight);
    this.renderer.shadowMap.enabled = true;
    this.renderer.shadowMap.type = THREE.PCFSoftShadowMap;
    this.renderer.setClearColor(0x87CEEB);
    document.body.appendChild(this.renderer.domElement);
  }

  setupScene() {
    this.scene = new THREE.Scene();
    this.scene.fog = new THREE.Fog(0x87CEEB, 50, 200);

    // Add skybox
    this.createSkybox();
  }

  createSkybox() {
    const skyGeometry = new THREE.SphereGeometry(500, 32, 32);
    const skyMaterial = new THREE.MeshBasicMaterial({
      color: 0x87CEEB,
      side: THREE.BackSide
    });
    const sky = new THREE.Mesh(skyGeometry, skyMaterial);
    this.scene.add(sky);

    // Add some clouds
    this.createClouds();
  }

  createClouds() {
    const cloudGeometry = new THREE.SphereGeometry(10, 8, 8);
    const cloudMaterial = new THREE.MeshBasicMaterial({
      color: 0xffffff,
      transparent: true,
      opacity: 0.7
    });

    for (let i = 0; i < 20; i++) {
      const cloud = new THREE.Mesh(cloudGeometry, cloudMaterial);
      cloud.position.set(
        (Math.random() - 0.5) * 400,
        50 + Math.random() * 50,
        (Math.random() - 0.5) * 400
      );
      cloud.scale.set(
        1 + Math.random() * 2,
        0.5 + Math.random() * 0.5,
        1 + Math.random() * 2
      );
      this.scene.add(cloud);
    }
  }

  setupCamera() {
    this.camera = new THREE.PerspectiveCamera(
      75,
      window.innerWidth / window.innerHeight,
      0.1,
      1000
    );
    this.camera.position.set(0, 10, 10);
  }

  setupLighting() {
    // Ambient light
    const ambientLight = new THREE.AmbientLight(0x404040, 0.4);
    this.scene.add(ambientLight);

    // Directional light (sun)
    const directionalLight = new THREE.DirectionalLight(0xffffff, 1.0);
    directionalLight.position.set(50, 100, 50);
    directionalLight.castShadow = true;
    directionalLight.shadow.mapSize.width = 2048;
    directionalLight.shadow.mapSize.height = 2048;
    directionalLight.shadow.camera.near = 0.5;
    directionalLight.shadow.camera.far = 200;
    directionalLight.shadow.camera.left = -100;
    directionalLight.shadow.camera.right = 100;
    directionalLight.shadow.camera.top = 100;
    directionalLight.shadow.camera.bottom = -100;
    this.scene.add(directionalLight);
  }

  createWorld() {
    // Ground
    const groundGeometry = new THREE.PlaneGeometry(200, 200);
    const groundMaterial = new THREE.MeshLambertMaterial({ color: 0x4a4a4a });
    const ground = new THREE.Mesh(groundGeometry, groundMaterial);
    ground.rotation.x = -Math.PI / 2;
    ground.receiveShadow = true;
    this.scene.add(ground);

    // Buildings
    this.createBuildings();

    // Trees
    this.createTrees();

    // Streets
    this.createStreets();

    // Cars
    this.createCars();

    // Street lights
    this.createStreetLights();

    // Particle effects
    this.createParticles();
  }

  createParticles() {
    // Create floating particles (dust/leaves)
    const particleCount = 100;
    const particles = new THREE.BufferGeometry();
    const positions = new Float32Array(particleCount * 3);
    const colors = new Float32Array(particleCount * 3);

    for (let i = 0; i < particleCount; i++) {
      positions[i * 3] = (Math.random() - 0.5) * 200;
      positions[i * 3 + 1] = Math.random() * 50 + 5;
      positions[i * 3 + 2] = (Math.random() - 0.5) * 200;

      const color = new THREE.Color();
      color.setHSL(0.1 + Math.random() * 0.2, 0.5, 0.5 + Math.random() * 0.3);
      colors[i * 3] = color.r;
      colors[i * 3 + 1] = color.g;
      colors[i * 3 + 2] = color.b;
    }

    particles.setAttribute('position', new THREE.BufferAttribute(positions, 3));
    particles.setAttribute('color', new THREE.BufferAttribute(colors, 3));

    const particleMaterial = new THREE.PointsMaterial({
      size: 0.5,
      vertexColors: true,
      transparent: true,
      opacity: 0.6
    });

    this.particles = new THREE.Points(particles, particleMaterial);
    this.scene.add(this.particles);
  }

  createBuildings() {
    const buildingConfigs = [
      { pos: [20, 0, 20], size: [8, 25, 8], color: 0xcccccc },
      { pos: [-20, 0, 20], size: [10, 18, 6], color: 0xaaaaaa },
      { pos: [30, 0, -15], size: [6, 30, 10], color: 0xbbbbbb },
      { pos: [-25, 0, -20], size: [12, 20, 8], color: 0x999999 },
      { pos: [15, 0, -35], size: [8, 22, 6], color: 0xaaaaaa },
      { pos: [-15, 0, 35], size: [10, 16, 8], color: 0xcccccc },
      { pos: [45, 0, 10], size: [6, 28, 8], color: 0x888888 },
      { pos: [-40, 0, 5], size: [8, 24, 10], color: 0xaaaaaa },
      { pos: [35, 0, -35], size: [12, 15, 12], color: 0xbbbbbb },
      { pos: [-35, 0, -35], size: [10, 20, 8], color: 0x999999 },
      { pos: [50, 0, -10], size: [8, 32, 6], color: 0xcccccc },
      { pos: [-50, 0, 15], size: [14, 18, 10], color: 0xaaaaaa },
    ];

    // Store buildings for collision detection
    this.buildings = [];

    buildingConfigs.forEach(config => {
      const geometry = new THREE.BoxGeometry(config.size[0], config.size[1], config.size[2]);
      const material = new THREE.MeshLambertMaterial({ color: config.color });
      const building = new THREE.Mesh(geometry, material);

      building.position.set(config.pos[0], config.size[1] / 2, config.pos[2]);
      building.castShadow = true;
      building.receiveShadow = true;

      // Add windows
      this.addWindows(building, config.size);

      this.scene.add(building);

      // Store for collision detection
      this.buildings.push({
        position: { x: config.pos[0], z: config.pos[2] },
        size: { x: config.size[0], z: config.size[2] }
      });
    });
  }

  addWindows(building, size) {
    const windowGeometry = new THREE.PlaneGeometry(0.8, 1.2);
    const windowMaterial = new THREE.MeshBasicMaterial({
      color: 0x4488ff,
      transparent: true,
      opacity: 0.6
    });

    const windowsPerRow = Math.floor(size[0] / 2);
    const rows = Math.floor(size[1] / 3);

    for (let row = 0; row < rows; row++) {
      for (let col = 0; col < windowsPerRow; col++) {
        const window = new THREE.Mesh(windowGeometry, windowMaterial);
        window.position.set(
          -size[0] / 2 + (col + 0.5) * (size[0] / windowsPerRow),
          -size[1] / 2 + (row + 1) * (size[1] / (rows + 1)),
          size[2] / 2 + 0.01
        );
        building.add(window);
      }
    }
  }

  createTrees() {
    const treePositions = [
      [8, 0, 8], [-8, 0, 8], [8, 0, -8], [-8, 0, -8],
      [25, 0, 0], [-25, 0, 0], [0, 0, 25], [0, 0, -25],
    ];

    treePositions.forEach(pos => {
      // Tree trunk
      const trunkGeometry = new THREE.CylinderGeometry(0.3, 0.4, 4);
      const trunkMaterial = new THREE.MeshLambertMaterial({ color: 0x8B4513 });
      const trunk = new THREE.Mesh(trunkGeometry, trunkMaterial);
      trunk.position.set(pos[0], 2, pos[2]);
      trunk.castShadow = true;
      this.scene.add(trunk);

      // Tree foliage
      const foliageGeometry = new THREE.SphereGeometry(3, 8, 6);
      const foliageMaterial = new THREE.MeshLambertMaterial({ color: 0x228B22 });
      const foliage = new THREE.Mesh(foliageGeometry, foliageMaterial);
      foliage.position.set(pos[0], 6, pos[2]);
      foliage.castShadow = true;
      this.scene.add(foliage);
    });
  }

  createStreets() {
    // Main horizontal street
    const street1Geometry = new THREE.PlaneGeometry(200, 8);
    const streetMaterial = new THREE.MeshLambertMaterial({ color: 0x333333 });
    const street1 = new THREE.Mesh(street1Geometry, streetMaterial);
    street1.rotation.x = -Math.PI / 2;
    street1.position.y = 0.01;
    street1.receiveShadow = true;
    this.scene.add(street1);

    // Main vertical street
    const street2Geometry = new THREE.PlaneGeometry(8, 200);
    const street2 = new THREE.Mesh(street2Geometry, streetMaterial);
    street2.rotation.x = -Math.PI / 2;
    street2.position.y = 0.01;
    street2.receiveShadow = true;
    this.scene.add(street2);

    // Add street markings
    const lineGeometry = new THREE.PlaneGeometry(180, 0.2);
    const lineMaterial = new THREE.MeshBasicMaterial({ color: 0xffffff });

    // Horizontal street lines
    for (let i = -2; i <= 2; i += 4) {
      const line = new THREE.Mesh(lineGeometry, lineMaterial);
      line.rotation.x = -Math.PI / 2;
      line.position.set(0, 0.02, i);
      this.scene.add(line);
    }

    // Vertical street lines
    const vLineGeometry = new THREE.PlaneGeometry(0.2, 180);
    for (let i = -2; i <= 2; i += 4) {
      const line = new THREE.Mesh(vLineGeometry, lineMaterial);
      line.rotation.x = -Math.PI / 2;
      line.position.set(i, 0.02, 0);
      this.scene.add(line);
    }
  }

  createCars() {
    const carPositions = [
      { pos: [10, 0.5, 2], rotation: 0, color: 0xff0000 },
      { pos: [-15, 0.5, -2], rotation: Math.PI, color: 0x0000ff },
      { pos: [25, 0.5, 2], rotation: 0, color: 0x00ff00 },
      { pos: [-30, 0.5, -2], rotation: Math.PI, color: 0xffff00 },
      { pos: [2, 0.5, 20], rotation: Math.PI/2, color: 0xff00ff },
      { pos: [-2, 0.5, -25], rotation: -Math.PI/2, color: 0x00ffff },
    ];

    carPositions.forEach(carConfig => {
      this.createCar(carConfig.pos, carConfig.rotation, carConfig.color);
    });
  }

  createCar(position, rotation, color) {
    const carGroup = new THREE.Group();

    // Car body
    const bodyGeometry = new THREE.BoxGeometry(4, 1.5, 2);
    const bodyMaterial = new THREE.MeshLambertMaterial({ color });
    const body = new THREE.Mesh(bodyGeometry, bodyMaterial);
    body.position.y = 0.75;
    body.castShadow = true;
    carGroup.add(body);

    // Car roof
    const roofGeometry = new THREE.BoxGeometry(2.5, 1, 1.8);
    const roofMaterial = new THREE.MeshLambertMaterial({ color: color * 0.8 });
    const roof = new THREE.Mesh(roofGeometry, roofMaterial);
    roof.position.y = 1.75;
    roof.castShadow = true;
    carGroup.add(roof);

    // Wheels
    const wheelGeometry = new THREE.CylinderGeometry(0.3, 0.3, 0.2);
    const wheelMaterial = new THREE.MeshLambertMaterial({ color: 0x222222 });

    const wheelPositions = [
      [-1.3, 0.3, -0.8], [1.3, 0.3, -0.8],
      [-1.3, 0.3, 0.8], [1.3, 0.3, 0.8]
    ];

    wheelPositions.forEach(pos => {
      const wheel = new THREE.Mesh(wheelGeometry, wheelMaterial);
      wheel.position.set(pos[0], pos[1], pos[2]);
      wheel.rotation.z = Math.PI / 2;
      wheel.castShadow = true;
      carGroup.add(wheel);
    });

    carGroup.position.set(position[0], position[1], position[2]);
    carGroup.rotation.y = rotation;
    this.scene.add(carGroup);
  }

  createStreetLights() {
    const lightPositions = [
      [6, 0, 6], [-6, 0, 6], [6, 0, -6], [-6, 0, -6],
      [18, 0, 6], [-18, 0, 6], [18, 0, -6], [-18, 0, -6],
      [6, 0, 18], [-6, 0, 18], [6, 0, -18], [-6, 0, -18],
    ];

    lightPositions.forEach(pos => {
      this.createStreetLight(pos);
    });
  }

  createStreetLight(position) {
    const lightGroup = new THREE.Group();

    // Pole
    const poleGeometry = new THREE.CylinderGeometry(0.1, 0.1, 8);
    const poleMaterial = new THREE.MeshLambertMaterial({ color: 0x444444 });
    const pole = new THREE.Mesh(poleGeometry, poleMaterial);
    pole.position.y = 4;
    pole.castShadow = true;
    lightGroup.add(pole);

    // Light fixture
    const fixtureGeometry = new THREE.SphereGeometry(0.5);
    const fixtureMaterial = new THREE.MeshBasicMaterial({
      color: 0xffffaa,
      transparent: true,
      opacity: 0.8
    });
    const fixture = new THREE.Mesh(fixtureGeometry, fixtureMaterial);
    fixture.position.y = 7.5;
    lightGroup.add(fixture);

    // Point light
    const pointLight = new THREE.PointLight(0xffffaa, 0.5, 20);
    pointLight.position.y = 7.5;
    pointLight.castShadow = true;
    lightGroup.add(pointLight);

    lightGroup.position.set(position[0], position[1], position[2]);
    this.scene.add(lightGroup);
  }

  createPlayer() {
    // Create simple player character (cube for now)
    const playerGeometry = new THREE.BoxGeometry(1, 2, 1);
    const playerMaterial = new THREE.MeshLambertMaterial({ color: 0xff0000 });
    this.player = new THREE.Mesh(playerGeometry, playerMaterial);
    this.player.position.set(0, 1, 0);
    this.player.castShadow = true;
    this.scene.add(this.player);

    // Create NPCs
    this.createNPCs();
  }

  createNPCs() {
    this.npcs = [];
    const npcPositions = [
      [12, 1, 12], [-12, 1, 12], [12, 1, -12], [-12, 1, -12],
      [25, 1, 8], [-25, 1, -8], [8, 1, 25], [-8, 1, -25],
      [35, 1, 15], [-35, 1, -15], [15, 1, 35], [-15, 1, -35],
    ];

    npcPositions.forEach((pos, index) => {
      const npcGeometry = new THREE.BoxGeometry(0.8, 1.8, 0.8);
      const npcMaterial = new THREE.MeshLambertMaterial({
        color: new THREE.Color().setHSL(index * 0.1, 0.7, 0.5)
      });
      const npc = new THREE.Mesh(npcGeometry, npcMaterial);
      npc.position.set(pos[0], pos[1], pos[2]);
      npc.castShadow = true;
      this.scene.add(npc);

      // Add NPC data for movement
      this.npcs.push({
        mesh: npc,
        speed: 1 + Math.random() * 2,
        direction: Math.random() * Math.PI * 2,
        changeTimer: 0,
        originalPosition: { x: pos[0], z: pos[2] }
      });
    });
  }

  setupControls() {
    // Keyboard controls
    document.addEventListener('keydown', (event) => {
      this.keys[event.code] = true;

      // Running
      if (event.code === 'ShiftLeft' || event.code === 'ShiftRight') {
        this.isRunning = true;
      }

      // ESC to exit pointer lock
      if (event.code === 'Escape') {
        document.exitPointerLock();
      }
    });

    document.addEventListener('keyup', (event) => {
      this.keys[event.code] = false;

      // Stop running
      if (event.code === 'ShiftLeft' || event.code === 'ShiftRight') {
        this.isRunning = false;
      }
    });

    // Mouse controls for camera
    document.addEventListener('click', () => {
      this.renderer.domElement.requestPointerLock();
    });

    document.addEventListener('pointerlockchange', () => {
      this.mouse.isLocked = document.pointerLockElement === this.renderer.domElement;
    });

    document.addEventListener('mousemove', (event) => {
      if (this.mouse.isLocked) {
        this.cameraAngle.horizontal -= event.movementX * 0.002;
        this.cameraAngle.vertical -= event.movementY * 0.002;

        // Limit vertical rotation
        this.cameraAngle.vertical = Math.max(-Math.PI/2, Math.min(Math.PI/2, this.cameraAngle.vertical));
      }
    });

    // Window resize
    window.addEventListener('resize', () => {
      this.camera.aspect = window.innerWidth / window.innerHeight;
      this.camera.updateProjectionMatrix();
      this.renderer.setSize(window.innerWidth, window.innerHeight);
    });
  }

  updateUI() {
    if (this.player) {
      const pos = this.player.position;
      const positionElement = document.getElementById('position');
      const speedElement = document.getElementById('speed');

      if (positionElement) {
        positionElement.textContent = `Kedudukan: (${Math.round(pos.x)}, ${Math.round(pos.y)}, ${Math.round(pos.z)})`;
      }

      if (speedElement) {
        speedElement.textContent = `Kelajuan: ${this.isRunning ? 'Berlari' : 'Berjalan'}`;
      }
    }
  }

  updatePlayer(deltaTime) {
    if (!this.player) return;

    const speed = this.isRunning ? this.runSpeed : this.playerSpeed;
    const moveDistance = speed * deltaTime;

    // Reset movement direction
    this.playerDirection.set(0, 0, 0);

    // Calculate movement based on camera direction
    const cameraDirection = new THREE.Vector3();
    this.camera.getWorldDirection(cameraDirection);
    cameraDirection.y = 0; // Keep movement on ground level
    cameraDirection.normalize();

    const rightDirection = new THREE.Vector3();
    rightDirection.crossVectors(cameraDirection, new THREE.Vector3(0, 1, 0));

    // WASD movement
    if (this.keys['KeyW']) {
      this.playerDirection.add(cameraDirection);
    }
    if (this.keys['KeyS']) {
      this.playerDirection.sub(cameraDirection);
    }
    if (this.keys['KeyA']) {
      this.playerDirection.sub(rightDirection);
    }
    if (this.keys['KeyD']) {
      this.playerDirection.add(rightDirection);
    }

    // Normalize and apply movement with collision detection
    if (this.playerDirection.length() > 0) {
      this.playerDirection.normalize();

      // Calculate new position
      const newPosition = this.player.position.clone();
      newPosition.add(this.playerDirection.multiplyScalar(moveDistance));

      // Check collision with buildings
      if (!this.checkCollision(newPosition)) {
        this.player.position.copy(newPosition);

        // Make player face movement direction
        this.player.lookAt(
          this.player.position.x + this.playerDirection.x,
          this.player.position.y,
          this.player.position.z + this.playerDirection.z
        );
      }
    }

    // Keep player on ground and within bounds
    this.player.position.y = 1;
    this.player.position.x = Math.max(-90, Math.min(90, this.player.position.x));
    this.player.position.z = Math.max(-90, Math.min(90, this.player.position.z));
  }

  checkCollision(position) {
    const playerRadius = 1; // Player collision radius

    // Check collision with buildings
    for (let building of this.buildings) {
      const dx = Math.abs(position.x - building.position.x);
      const dz = Math.abs(position.z - building.position.z);

      if (dx < (building.size.x / 2 + playerRadius) &&
          dz < (building.size.z / 2 + playerRadius)) {
        return true; // Collision detected
      }
    }

    return false; // No collision
  }

  updateCamera() {
    if (!this.player) return;

    // Third-person camera following player
    const cameraOffset = new THREE.Vector3(
      Math.sin(this.cameraAngle.horizontal) * this.cameraDistance,
      this.cameraHeight + Math.sin(this.cameraAngle.vertical) * this.cameraDistance,
      Math.cos(this.cameraAngle.horizontal) * this.cameraDistance
    );

    this.camera.position.copy(this.player.position).add(cameraOffset);
    this.camera.lookAt(this.player.position);
  }

  updateNPCs(deltaTime) {
    if (!this.npcs) return;

    this.npcs.forEach(npc => {
      // Update direction change timer
      npc.changeTimer += deltaTime;

      // Change direction randomly every 3-7 seconds
      if (npc.changeTimer > 3 + Math.random() * 4) {
        npc.direction = Math.random() * Math.PI * 2;
        npc.changeTimer = 0;
      }

      // Calculate movement
      const moveX = Math.sin(npc.direction) * npc.speed * deltaTime;
      const moveZ = Math.cos(npc.direction) * npc.speed * deltaTime;

      // Apply movement
      npc.mesh.position.x += moveX;
      npc.mesh.position.z += moveZ;

      // Keep NPCs within bounds and away from buildings
      const newPos = { x: npc.mesh.position.x, z: npc.mesh.position.z };
      if (this.checkCollision(new THREE.Vector3(newPos.x, 1, newPos.z))) {
        // Reverse direction if collision
        npc.direction += Math.PI;
        npc.mesh.position.x -= moveX * 2;
        npc.mesh.position.z -= moveZ * 2;
      }

      // Keep in world bounds
      npc.mesh.position.x = Math.max(-80, Math.min(80, npc.mesh.position.x));
      npc.mesh.position.z = Math.max(-80, Math.min(80, npc.mesh.position.z));

      // Face movement direction
      npc.mesh.rotation.y = npc.direction;
    });
  }

  updateParticles(deltaTime) {
    if (this.particles) {
      // Rotate particles slowly
      this.particles.rotation.y += deltaTime * 0.1;

      // Move particles up and down
      const positions = this.particles.geometry.attributes.position.array;
      for (let i = 0; i < positions.length; i += 3) {
        positions[i + 1] += Math.sin(Date.now() * 0.001 + i) * 0.01;

        // Reset particle if it goes too high
        if (positions[i + 1] > 60) {
          positions[i + 1] = 5;
        }
      }
      this.particles.geometry.attributes.position.needsUpdate = true;
    }
  }

  animate() {
    requestAnimationFrame(() => this.animate());

    const deltaTime = this.clock.getDelta();

    this.updatePlayer(deltaTime);
    this.updateNPCs(deltaTime);
    this.updateParticles(deltaTime);
    this.updateCamera();
    this.updateUI();

    this.renderer.render(this.scene, this.camera);
  }
}

// Initialize game when page loads
window.addEventListener('load', () => {
  new OpenWorldGame();
});