// Simple 3D Open World Game - Working Version
import * as THREE from 'https://esm.sh/three@0.160.0';

class SimpleOpenWorldGame {
  constructor() {
    console.log('Starting Simple Open World Game...');
    
    this.scene = null;
    this.camera = null;
    this.renderer = null;
    this.clock = new THREE.Clock();
    
    // Player
    this.player = null;
    this.playerSpeed = 5;
    this.runSpeed = 10;
    this.isRunning = false;
    
    // Camera
    this.cameraDistance = 10;
    this.cameraHeight = 5;
    this.cameraAngle = { horizontal: 0, vertical: 0.3 };
    
    // Input
    this.keys = {};
    this.mouse = { isLocked: false };
    
    this.init();
  }

  init() {
    try {
      console.log('Initializing renderer...');
      this.setupRenderer();
      
      console.log('Setting up scene...');
      this.setupScene();
      
      console.log('Setting up camera...');
      this.setupCamera();
      
      console.log('Setting up lighting...');
      this.setupLighting();
      
      console.log('Creating world...');
      this.createWorld();
      
      console.log('Creating player...');
      this.createPlayer();
      
      console.log('Setting up controls...');
      this.setupControls();
      
      console.log('Hiding loading screen...');
      this.hideLoading();
      
      console.log('Starting animation loop...');
      this.animate();
      
      console.log('Game initialized successfully!');
    } catch (error) {
      console.error('Error initializing game:', error);
    }
  }

  hideLoading() {
    const loading = document.getElementById('loading');
    if (loading) {
      loading.style.opacity = '0';
      setTimeout(() => {
        loading.style.display = 'none';
      }, 500);
    }
  }

  setupRenderer() {
    this.renderer = new THREE.WebGLRenderer({ antialias: true });
    this.renderer.setSize(window.innerWidth, window.innerHeight);
    this.renderer.shadowMap.enabled = true;
    this.renderer.shadowMap.type = THREE.PCFSoftShadowMap;
    this.renderer.setClearColor(0x87CEEB);
    document.body.appendChild(this.renderer.domElement);
  }

  setupScene() {
    this.scene = new THREE.Scene();
    this.scene.fog = new THREE.Fog(0x87CEEB, 50, 200);
  }

  setupCamera() {
    this.camera = new THREE.PerspectiveCamera(75, window.innerWidth / window.innerHeight, 0.1, 1000);
    this.camera.position.set(0, 10, 10);
  }

  setupLighting() {
    // Ambient light
    const ambientLight = new THREE.AmbientLight(0x404040, 0.4);
    this.scene.add(ambientLight);

    // Directional light (sun)
    const directionalLight = new THREE.DirectionalLight(0xffffff, 1.0);
    directionalLight.position.set(50, 100, 50);
    directionalLight.castShadow = true;
    this.scene.add(directionalLight);
  }

  createWorld() {
    // Ground
    const groundGeometry = new THREE.PlaneGeometry(200, 200);
    const groundMaterial = new THREE.MeshLambertMaterial({ color: 0x4a4a4a });
    const ground = new THREE.Mesh(groundGeometry, groundMaterial);
    ground.rotation.x = -Math.PI / 2;
    ground.receiveShadow = true;
    this.scene.add(ground);

    // Simple buildings
    this.createBuildings();
    
    // Trees
    this.createTrees();
    
    // Streets
    this.createStreets();
  }

  createBuildings() {
    const buildings = [
      { pos: [20, 0, 20], size: [8, 25, 8], color: 0xcccccc },
      { pos: [-20, 0, 20], size: [10, 18, 6], color: 0xaaaaaa },
      { pos: [30, 0, -15], size: [6, 30, 10], color: 0xbbbbbb },
      { pos: [-25, 0, -20], size: [12, 20, 8], color: 0x999999 },
    ];

    buildings.forEach(config => {
      const geometry = new THREE.BoxGeometry(config.size[0], config.size[1], config.size[2]);
      const material = new THREE.MeshLambertMaterial({ color: config.color });
      const building = new THREE.Mesh(geometry, material);
      
      building.position.set(config.pos[0], config.size[1] / 2, config.pos[2]);
      building.castShadow = true;
      building.receiveShadow = true;
      
      this.scene.add(building);
    });
  }

  createTrees() {
    const treePositions = [[8, 0, 8], [-8, 0, 8], [8, 0, -8], [-8, 0, -8]];

    treePositions.forEach(pos => {
      // Trunk
      const trunkGeometry = new THREE.CylinderGeometry(0.3, 0.4, 4);
      const trunkMaterial = new THREE.MeshLambertMaterial({ color: 0x8B4513 });
      const trunk = new THREE.Mesh(trunkGeometry, trunkMaterial);
      trunk.position.set(pos[0], 2, pos[2]);
      trunk.castShadow = true;
      this.scene.add(trunk);

      // Foliage
      const foliageGeometry = new THREE.SphereGeometry(3, 8, 6);
      const foliageMaterial = new THREE.MeshLambertMaterial({ color: 0x228B22 });
      const foliage = new THREE.Mesh(foliageGeometry, foliageMaterial);
      foliage.position.set(pos[0], 6, pos[2]);
      foliage.castShadow = true;
      this.scene.add(foliage);
    });
  }

  createStreets() {
    // Horizontal street
    const street1Geometry = new THREE.PlaneGeometry(200, 8);
    const streetMaterial = new THREE.MeshLambertMaterial({ color: 0x333333 });
    const street1 = new THREE.Mesh(street1Geometry, streetMaterial);
    street1.rotation.x = -Math.PI / 2;
    street1.position.y = 0.01;
    this.scene.add(street1);

    // Vertical street
    const street2Geometry = new THREE.PlaneGeometry(8, 200);
    const street2 = new THREE.Mesh(street2Geometry, streetMaterial);
    street2.rotation.x = -Math.PI / 2;
    street2.position.y = 0.01;
    this.scene.add(street2);
  }

  createPlayer() {
    const playerGeometry = new THREE.BoxGeometry(1, 2, 1);
    const playerMaterial = new THREE.MeshLambertMaterial({ color: 0xff0000 });
    this.player = new THREE.Mesh(playerGeometry, playerMaterial);
    this.player.position.set(0, 1, 0);
    this.player.castShadow = true;
    this.scene.add(this.player);
  }

  setupControls() {
    // Keyboard
    document.addEventListener('keydown', (event) => {
      this.keys[event.code] = true;
      if (event.code === 'ShiftLeft' || event.code === 'ShiftRight') {
        this.isRunning = true;
      }
    });

    document.addEventListener('keyup', (event) => {
      this.keys[event.code] = false;
      if (event.code === 'ShiftLeft' || event.code === 'ShiftRight') {
        this.isRunning = false;
      }
    });

    // Mouse
    document.addEventListener('click', () => {
      this.renderer.domElement.requestPointerLock();
    });

    document.addEventListener('pointerlockchange', () => {
      this.mouse.isLocked = document.pointerLockElement === this.renderer.domElement;
    });

    document.addEventListener('mousemove', (event) => {
      if (this.mouse.isLocked) {
        this.cameraAngle.horizontal -= event.movementX * 0.002;
        this.cameraAngle.vertical -= event.movementY * 0.002;
        this.cameraAngle.vertical = Math.max(-Math.PI/2, Math.min(Math.PI/2, this.cameraAngle.vertical));
      }
    });

    // Resize
    window.addEventListener('resize', () => {
      this.camera.aspect = window.innerWidth / window.innerHeight;
      this.camera.updateProjectionMatrix();
      this.renderer.setSize(window.innerWidth, window.innerHeight);
    });
  }

  updatePlayer(deltaTime) {
    if (!this.player) return;

    const speed = this.isRunning ? this.runSpeed : this.playerSpeed;
    const moveDistance = speed * deltaTime;

    const playerDirection = new THREE.Vector3(0, 0, 0);

    // Calculate movement based on camera direction
    const cameraDirection = new THREE.Vector3();
    this.camera.getWorldDirection(cameraDirection);
    cameraDirection.y = 0;
    cameraDirection.normalize();

    const rightDirection = new THREE.Vector3();
    rightDirection.crossVectors(cameraDirection, new THREE.Vector3(0, 1, 0));

    // WASD movement
    if (this.keys['KeyW']) playerDirection.add(cameraDirection);
    if (this.keys['KeyS']) playerDirection.sub(cameraDirection);
    if (this.keys['KeyA']) playerDirection.sub(rightDirection);
    if (this.keys['KeyD']) playerDirection.add(rightDirection);

    // Apply movement
    if (playerDirection.length() > 0) {
      playerDirection.normalize();
      this.player.position.add(playerDirection.multiplyScalar(moveDistance));
      
      // Face movement direction
      this.player.lookAt(
        this.player.position.x + playerDirection.x,
        this.player.position.y,
        this.player.position.z + playerDirection.z
      );
    }

    // Keep on ground and in bounds
    this.player.position.y = 1;
    this.player.position.x = Math.max(-90, Math.min(90, this.player.position.x));
    this.player.position.z = Math.max(-90, Math.min(90, this.player.position.z));
  }

  updateCamera() {
    if (!this.player) return;

    // Third-person camera
    const cameraOffset = new THREE.Vector3(
      Math.sin(this.cameraAngle.horizontal) * this.cameraDistance,
      this.cameraHeight + Math.sin(this.cameraAngle.vertical) * this.cameraDistance,
      Math.cos(this.cameraAngle.horizontal) * this.cameraDistance
    );

    this.camera.position.copy(this.player.position).add(cameraOffset);
    this.camera.lookAt(this.player.position);
  }

  updateUI() {
    if (this.player) {
      const pos = this.player.position;
      const positionElement = document.getElementById('position');
      const speedElement = document.getElementById('speed');
      
      if (positionElement) {
        positionElement.textContent = `Kedudukan: (${Math.round(pos.x)}, ${Math.round(pos.y)}, ${Math.round(pos.z)})`;
      }
      
      if (speedElement) {
        speedElement.textContent = `Kelajuan: ${this.isRunning ? 'Berlari' : 'Berjalan'}`;
      }
    }
  }

  animate() {
    requestAnimationFrame(() => this.animate());
    
    const deltaTime = this.clock.getDelta();
    
    this.updatePlayer(deltaTime);
    this.updateCamera();
    this.updateUI();
    
    this.renderer.render(this.scene, this.camera);
  }
}

// Start game when page loads
window.addEventListener('load', () => {
  console.log('Page loaded, starting game...');
  new SimpleOpenWorldGame();
});
