// 3D Open World Game Configuration
export const GameConfig = {
  // Player settings
  player: {
    walkSpeed: 5,
    runSpeed: 10,
    size: { width: 1, height: 2, depth: 1 },
    color: 0xff0000,
    collisionRadius: 1
  },

  // Camera settings
  camera: {
    fov: 75,
    near: 0.1,
    far: 1000,
    distance: 10,
    height: 5,
    rotationSpeed: 0.002
  },

  // World settings
  world: {
    size: 200,
    groundColor: 0x4a4a4a,
    skyColor: 0x87CEEB,
    fogNear: 50,
    fogFar: 200
  },

  // Building configurations
  buildings: [
    { pos: [20, 0, 20], size: [8, 25, 8], color: 0xcccccc },
    { pos: [-20, 0, 20], size: [10, 18, 6], color: 0xaaaaaa },
    { pos: [30, 0, -15], size: [6, 30, 10], color: 0xbbbbbb },
    { pos: [-25, 0, -20], size: [12, 20, 8], color: 0x999999 },
    { pos: [15, 0, -35], size: [8, 22, 6], color: 0xaaaaaa },
    { pos: [-15, 0, 35], size: [10, 16, 8], color: 0xcccccc },
    { pos: [45, 0, 10], size: [6, 28, 8], color: 0x888888 },
    { pos: [-40, 0, 5], size: [8, 24, 10], color: 0xaaaaaa },
    { pos: [35, 0, -35], size: [12, 15, 12], color: 0xbbbbbb },
    { pos: [-35, 0, -35], size: [10, 20, 8], color: 0x999999 },
    { pos: [50, 0, -10], size: [8, 32, 6], color: 0xcccccc },
    { pos: [-50, 0, 15], size: [14, 18, 10], color: 0xaaaaaa }
  ],

  // Tree positions
  trees: [
    [8, 0, 8], [-8, 0, 8], [8, 0, -8], [-8, 0, -8],
    [25, 0, 0], [-25, 0, 0], [0, 0, 25], [0, 0, -25],
    [15, 0, 15], [-15, 0, -15], [35, 0, -5], [-35, 0, 5]
  ],

  // Car configurations
  cars: [
    { pos: [10, 0.5, 2], rotation: 0, color: 0xff0000 },
    { pos: [-15, 0.5, -2], rotation: Math.PI, color: 0x0000ff },
    { pos: [25, 0.5, 2], rotation: 0, color: 0x00ff00 },
    { pos: [-30, 0.5, -2], rotation: Math.PI, color: 0xffff00 },
    { pos: [2, 0.5, 20], rotation: Math.PI/2, color: 0xff00ff },
    { pos: [-2, 0.5, -25], rotation: -Math.PI/2, color: 0x00ffff }
  ],

  // Street light positions
  streetLights: [
    [6, 0, 6], [-6, 0, 6], [6, 0, -6], [-6, 0, -6],
    [18, 0, 6], [-18, 0, 6], [18, 0, -6], [-18, 0, -6],
    [6, 0, 18], [-6, 0, 18], [6, 0, -18], [-6, 0, -18]
  ],

  // NPC configurations
  npcs: [
    [12, 1, 12], [-12, 1, 12], [12, 1, -12], [-12, 1, -12],
    [25, 1, 8], [-25, 1, -8], [8, 1, 25], [-8, 1, -25],
    [35, 1, 15], [-35, 1, -15], [15, 1, 35], [-15, 1, -35]
  ],

  // Lighting settings
  lighting: {
    ambient: {
      color: 0x404040,
      intensity: 0.4
    },
    directional: {
      color: 0xffffff,
      intensity: 1.0,
      position: [50, 100, 50],
      shadowMapSize: 2048
    },
    streetLight: {
      color: 0xffffaa,
      intensity: 0.5,
      distance: 20
    }
  },

  // Particle settings
  particles: {
    count: 100,
    size: 0.5,
    opacity: 0.6,
    speed: 0.1
  },

  // UI settings
  ui: {
    updateInterval: 100, // milliseconds
    showPosition: true,
    showSpeed: true,
    showFPS: false
  },

  // Performance settings
  performance: {
    shadowMapEnabled: true,
    fogEnabled: true,
    particlesEnabled: true,
    antialiasing: true
  }
};
