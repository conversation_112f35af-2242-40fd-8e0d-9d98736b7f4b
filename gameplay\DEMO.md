# 🎮 Demo 3D Open World Game

## 🚀 Cara Menjalankan Demo

1. **Pastikan XAMPP berjalan**
   - Buka XAMPP Control Panel
   - Start Apache server
   - Pastikan status Apache adalah "Running"

2. **Buka Game**
   - Buka browser (Chrome/Firefox/Edge)
   - Pergi ke: `http://localhost/storygame/gameplay/index.html`
   - Tunggu game load sepenuhnya

3. **<PERSON><PERSON><PERSON>**
   - Klik pada skrin untuk mengaktifkan kawalan
   - Gunakan WASD untuk bergerak
   - Gunakan mouse untuk melihat sekeliling

## 🎯 Demo Scenario

### Scenario 1: Penjelajahan Asas
1. **<PERSON><PERSON>**
   - Gunakan W, A, S, D untuk bergerak
   - Lihat sekeliling dengan mouse
   - Perhatikan UI menunjukkan kedudukan

2. **Berlari**
   - <PERSON><PERSON> + W untuk berlari ke hadapan
   - Perhatikan perubahan kelajuan dalam UI
   - <PERSON><PERSON> perbezaan kelajuan

### Scenario 2: Jelajahi Bandar
1. **<PERSON><PERSON> ke <PERSON>**
   - <PERSON><PERSON> ke arah bangunan tinggi
   - Cuba untuk melalui bangunan (tidak boleh - ada collision)
   - <PERSON><PERSON><PERSON><PERSON> mengelilingi bangunan

2. **<PERSON><PERSON>jahi Jalan Raya**
   - Pergi ke jalan raya utama
   - Lihat kereta-kereta yang diparkir
   - Perhatikan garisan jalan putih

3. **Kawasan Pokok**
   - Pergi ke kawasan yang ada pokok
   - Lihat bayangan pokok di tanah
   - Perhatikan warna hijau daun

### Scenario 3: Interaksi dengan NPCs
1. **Cari NPCs**
   - Lihat sekeliling untuk cari kotak berwarna (NPCs)
   - Perhatikan mereka bergerak secara automatik
   - Ikut salah satu NPC untuk melihat pergerakannya

2. **Collision dengan NPCs**
   - Cuba untuk hampir dengan NPC
   - Perhatikan mereka mengelak halangan

### Scenario 4: Efek Visual
1. **Partikel**
   - Lihat ke atas untuk melihat partikel terapung
   - Perhatikan pergerakan partikel yang perlahan

2. **Pencahayaan**
   - Pergi dekat lampu jalan
   - Lihat cahaya kuning dari lampu
   - Perhatikan bayangan yang tercipta

3. **Skybox dan Awan**
   - Lihat ke atas untuk melihat langit biru
   - Perhatikan awan putih di langit
   - Lihat horizon yang kabur (fog effect)

### Scenario 5: Test Sempadan
1. **Pergi ke Pinggir Dunia**
   - Bergerak ke arah pinggir map
   - Perhatikan karakter berhenti di sempadan
   - Cuba bergerak ke arah yang berbeza

2. **Test Collision**
   - Cuba melalui semua bangunan
   - Pastikan tidak boleh melalui mana-mana bangunan
   - Test collision dari semua arah

## 🎮 Kawalan Lengkap

| Kawalan | Fungsi |
|---------|--------|
| **W** | Bergerak ke hadapan |
| **S** | Bergerak ke belakang |
| **A** | Bergerak ke kiri |
| **D** | Bergerak ke kanan |
| **Shift** | Berlari (tahan sambil bergerak) |
| **Mouse** | Lihat sekeliling |
| **Klik** | Aktifkan kawalan kamera |
| **ESC** | Nyahaktif kawalan kamera |

## 📊 Ciri-ciri untuk Diperhatikan

### ✅ Sistem Pergerakan
- [x] Pergerakan WASD yang smooth
- [x] Berlari dengan Shift
- [x] Kawalan kamera dengan mouse
- [x] Collision detection dengan bangunan

### ✅ Dunia 3D
- [x] 12+ bangunan dengan saiz berbeza
- [x] Sistem jalan raya dengan garisan
- [x] Pokok-pokok dengan bayangan
- [x] 6 kereta berwarna berbeza
- [x] 12 lampu jalan dengan cahaya

### ✅ NPCs dan AI
- [x] 12 NPCs yang bergerak automatik
- [x] AI yang mengelak halangan
- [x] Pergerakan rawak yang realistik
- [x] Warna berbeza untuk setiap NPC

### ✅ Efek Visual
- [x] 100 partikel terapung
- [x] Sistem bayangan realistik
- [x] Skybox dengan awan
- [x] Fog effect untuk jarak jauh
- [x] Pencahayaan ambient dan directional

### ✅ UI dan Feedback
- [x] Maklumat kedudukan real-time
- [x] Status kelajuan (berjalan/berlari)
- [x] Panduan kawalan
- [x] Loading screen

## 🔧 Troubleshooting Demo

### Masalah Biasa

**Game tidak load:**
- Periksa XAMPP Apache berjalan
- Refresh browser
- Periksa console untuk error

**Kawalan tidak berfungsi:**
- Klik pada skrin game
- Pastikan pointer lock aktif
- Tekan ESC dan klik semula

**Prestasi rendah:**
- Tutup tab browser lain
- Gunakan browser terkini
- Kurangkan zoom browser

**Skrin hitam:**
- Tunggu loading selesai
- Periksa WebGL support browser
- Restart browser

## 📈 Metrics Prestasi

Game ini dioptimumkan untuk:
- **FPS**: 60fps pada kebanyakan komputer
- **Memory**: <100MB RAM usage
- **Loading**: <5 saat pada koneksi normal
- **Compatibility**: Chrome, Firefox, Edge, Safari

## 🎯 Objektif Demo

Selepas demo ini, anda sepatutnya dapat:
1. ✅ Mengawal karakter dengan lancar
2. ✅ Menjelajahi seluruh dunia 3D
3. ✅ Memahami sistem collision
4. ✅ Melihat semua efek visual
5. ✅ Berinteraksi dengan NPCs
6. ✅ Menggunakan semua kawalan

## 🎉 Selamat Bermain!

Game ini menunjukkan kemampuan asas 3D open world seperti GTA. Anda boleh:
- Berjalan dan berlari dengan bebas
- Menjelajahi bandar yang hidup
- Melihat NPCs yang bergerak
- Menikmati efek visual yang menarik
- Merasai pengalaman 3D yang immersive

**Tip**: Cuba jelajahi setiap sudut dunia untuk menemui semua ciri yang tersembunyi!
