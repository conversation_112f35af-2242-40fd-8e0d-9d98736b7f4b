<!DOCTYPE html>
<html lang="ms">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>3D Open World Game</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            background: #000;
            font-family: Arial, sans-serif;
            overflow: hidden;
            color: white;
        }
        
        #loading {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 1000;
            color: white;
            font-size: 24px;
            transition: opacity 0.5s ease;
        }
        
        #ui {
            position: fixed;
            top: 20px;
            left: 20px;
            color: white;
            font-size: 14px;
            z-index: 100;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.8);
            background: rgba(0,0,0,0.5);
            padding: 15px;
            border-radius: 10px;
        }
        
        #ui h3 {
            margin: 0 0 10px 0;
            color: #4CAF50;
        }
        
        #ui div {
            margin: 5px 0;
        }
        
        #crosshair {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 20px;
            height: 20px;
            z-index: 50;
        }
        
        #crosshair::before,
        #crosshair::after {
            content: '';
            position: absolute;
            background: white;
        }
        
        #crosshair::before {
            width: 2px;
            height: 20px;
            left: 9px;
            top: 0;
        }
        
        #crosshair::after {
            width: 20px;
            height: 2px;
            left: 0;
            top: 9px;
        }
        
        canvas {
            display: block;
        }
    </style>
</head>
<body>
    <div id="loading">Memuatkan Dunia 3D...</div>
    
    <div id="ui">
        <h3>🎮 Kawalan Game</h3>
        <div><strong>WASD:</strong> Bergerak</div>
        <div><strong>Mouse:</strong> Lihat sekeliling</div>
        <div><strong>Shift:</strong> Berlari</div>
        <div><strong>Klik:</strong> Kunci kamera</div>
        <div><strong>ESC:</strong> Lepas kamera</div>
        <hr style="margin: 10px 0; border-color: #666;">
        <div id="position">Kedudukan: (0, 0, 0)</div>
        <div id="speed">Kelajuan: Berjalan</div>
    </div>
    
    <div id="crosshair"></div>
    
    <script type="module" src="openworld.js"></script>
</body>
</html>
