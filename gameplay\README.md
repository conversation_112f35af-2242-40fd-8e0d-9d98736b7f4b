# 🎮 3D Open World Game

Selamat datang ke dunia 3D open world yang menarik! Game ini membolehkan anda menjelajahi bandar virtual dengan karakter yang boleh bergerak bebas seperti dalam game GTA.

## ✨ Ciri-ciri Game

### 🏃‍♂️ Pergerakan Karakter
- **WASD**: <PERSON><PERSON> ke hadapan, be<PERSON><PERSON>, kiri, dan kanan
- **Shift**: <PERSON><PERSON><PERSON> (kelajuan tinggi)
- **Mouse**: <PERSON><PERSON> sekeliling dan kawalan kamera
- **ESC**: Keluar dari mod kamera terkunci

### 🏙️ Dunia 3D
- **Bangunan**: Pelbagai bangunan dengan tinggi dan warna berbeza
- **Jalan Raya**: Sistem jalan dengan garisan putih
- **Pokok**: Pokok-pokok hijau di sekeliling bandar
- **Kereta**: Kenderaan statik di jalan raya
- **Lampu Jalan**: Pencahayaan malam yang realistik
- **NPCs**: <PERSON><PERSON><PERSON> bukan pemain yang bergerak secara automatik

### 🌟 Efek Visual
- **Skybox**: Langit biru dengan awan putih
- **Partikel**: Zarah terapung untuk suasana
- **Bayangan**: Sistem bayangan realistik
- **Fog**: Kesan kabut untuk jarak jauh
- **Pencahayaan**: Sistem cahaya ambient dan directional

### 🎯 Sistem Collision
- Karakter tidak boleh melalui bangunan
- NPCs mengelak halangan secara automatik
- Sempadan dunia untuk mengehadkan pergerakan

## 🚀 Cara Menjalankan

1. **Pastikan XAMPP berjalan** dengan Apache server
2. **Buka browser** dan pergi ke:
   ```
   http://localhost/storygame/gameplay/index.html
   ```
3. **Klik pada skrin** untuk mengaktifkan kawalan kamera
4. **Gunakan WASD** untuk bergerak
5. **Tahan Shift** untuk berlari

## 🎮 Tips Bermain

### Kawalan Asas
- Klik pada skrin untuk mengunci kamera
- Gunakan mouse untuk melihat sekeliling
- Tekan ESC untuk membuka kunci kamera
- Tahan Shift sambil bergerak untuk berlari

### Penjelajahan
- Jelajahi seluruh bandar dengan bebas
- Lihat NPCs yang bergerak secara automatik
- Perhatikan kereta-kereta di jalan raya
- Nikmati efek visual seperti partikel dan pencahayaan

### Maklumat UI
- **Kedudukan**: Menunjukkan koordinat semasa karakter
- **Kelajuan**: Menunjukkan sama ada sedang berjalan atau berlari
- **Kawalan**: Senarai kawalan yang tersedia

## 🛠️ Teknologi Yang Digunakan

- **Three.js**: Library 3D JavaScript
- **WebGL**: Rendering 3D dalam browser
- **HTML5**: Struktur halaman web
- **CSS3**: Styling dan layout
- **JavaScript ES6**: Logik game dan interaksi

## 🎨 Ciri-ciri Teknikal

### Rendering
- WebGL dengan antialiasing
- Shadow mapping untuk bayangan realistik
- Fog rendering untuk kesan jarak
- Particle system untuk efek visual

### Fizik
- Collision detection untuk bangunan
- Boundary checking untuk sempadan dunia
- Smooth camera movement
- NPC AI untuk pergerakan automatik

### Optimisasi
- Efficient geometry untuk prestasi baik
- LOD (Level of Detail) untuk objek jauh
- Culling untuk objek di luar pandangan
- Memory management untuk model 3D

## 🔧 Customization

Anda boleh mengubah suai game ini dengan:

1. **Menambah bangunan baru** dalam fungsi `createBuildings()`
2. **Mengubah warna dan saiz** objek dalam konfigurasi
3. **Menambah NPCs** dengan mengubah array `npcPositions`
4. **Mengubah kelajuan** dengan mengubah `playerSpeed` dan `runSpeed`
5. **Menambah efek visual** baru dalam fungsi `createParticles()`

## 🐛 Troubleshooting

### Game tidak load
- Pastikan XAMPP Apache server berjalan
- Periksa console browser untuk error
- Pastikan semua fail berada dalam folder yang betul

### Prestasi rendah
- Tutup tab browser lain
- Kurangkan bilangan partikel
- Gunakan browser yang menyokong WebGL dengan baik

### Kawalan tidak berfungsi
- Klik pada skrin untuk mengaktifkan kawalan
- Pastikan browser menyokong Pointer Lock API
- Refresh halaman jika perlu

## 📝 Nota Pembangunan

Game ini dibina menggunakan pendekatan modular dengan:
- Sistem rendering yang cekap
- Collision detection yang mudah
- AI NPCs yang simple
- UI yang responsif
- Kod yang mudah difahami dan diubah suai

Selamat bermain dan menjelajahi dunia 3D! 🎉
